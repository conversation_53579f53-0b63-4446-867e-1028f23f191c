exports.up = function(knex) {
    return knex.schema.raw(`
        -- CREATE TABLE "payment_session" ---------------------------------------------------------------
        CREATE TABLE IF NOT EXISTS "public"."payment_session"
            (
                "payment_session_id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                "amount" NUMERIC(8,2) NOT NULL,
                "provider_payment_intent_id" TEXT NOT NULL,
                "payment_provider" TEXT NOT NULL,
                "status" TEXT NOT NULL,
                "event_id" INT NOT NULL,
                "user_id" INT NOT NULL,
                "data" JSONB,
                "created" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
                "modified" TIMESTAMP WITH TIME ZONE DEFAULT NULL,

                FOREIGN KEY (user_id) REFERENCES "user" (user_id),
                FOREI<PERSON><PERSON>EY (event_id) REFERENCES "event" (event_id)
            );
        ----------------------------------------------------------------------------------------------------------------

        -- CREATE TABLE "payment_session_team" ---------------------------------------------------------------
        CREATE TABLE IF NOT EXISTS "public"."payment_session_team"
            (
                "payment_session_team_id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                "payment_session_id" INT NOT NULL,
                "name" TEXT NOT NULL,
                "roster_team_id" INT NOT NULL,
                "amount" NUMERIC(8,2) NOT NULL,
                "surcharge" NUMERIC(8,2),
                "division_id" INT,
                "division_fee" NUMERIC(8,2),
                "team_sw_fee" NUMERIC(8,2) DEFAULT 0,
                "discount" NUMERIC,
                "created" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
                "modified" TIMESTAMP WITH TIME ZONE DEFAULT NULL,

                FOREIGN KEY (payment_session_id) REFERENCES "payment_session" (payment_session_id),
                CONSTRAINT unique_payment_roster UNIQUE (payment_session_id, roster_team_id)
            );
        ----------------------------------------------------------------------------------------------------------------

        --- Add modified trigger to "payment_session" table ---------------------------------------------------------
        DROP TRIGGER IF EXISTS "update_payment_session_modified" ON "public"."payment_session";
        CREATE TRIGGER "update_payment_session_modified"
            BEFORE UPDATE
            ON "public"."payment_session"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        ----------------------------------------------------------------------------------------------------------------

        --- Add modified trigger to "payment_session_team" table ---------------------------------------------------------
        DROP TRIGGER IF EXISTS "update_payment_session_team_modified" ON "public"."payment_session_team";
        CREATE TRIGGER "update_payment_session_team_modified"
            BEFORE UPDATE
            ON "public"."payment_session_team"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        ----------------------------------------------------------------------------------------------------------------
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."payment_session_team";
        DROP TABLE IF EXISTS "public"."payment_session";
    `);
};
