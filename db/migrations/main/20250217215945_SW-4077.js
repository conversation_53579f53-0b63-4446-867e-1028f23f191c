/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Only rename column if it exists
        DO $$
        BEGIN
            IF EXISTS (SELECT 1 FROM information_schema.columns
                      WHERE table_name = 'sportengine_adult_role'
                      AND column_name = 'is_coach') THEN
                ALTER TABLE sportengine_adult_role RENAME COLUMN is_coach TO is_impact;
            END IF;
        END $$;

        alter table sportengine_adult_role
            drop column if exists is_chaperone;
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        alter table sportengine_adult_role 
            rename column is_impact to is_coach;

        alter table sportengine_adult_role 
            add column if not exists is_chaperone boolean default false;
    `)
};
