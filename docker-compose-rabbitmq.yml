version: '3.8'

services:
    rabbitmq:
        image: rabbitmq:3-management
        container_name: temp-rabbitmq
        ports:
            - "5672:5672"
            - "15672:15672"
        environment:
            RABBITMQ_DEFAULT_USER: rabbitmq
            RABBITMQ_DEFAULT_PASS: change-in-production
            RABBITMQ_DEFAULT_VHOST: email-service
        volumes:
            - ./rabbitmq-init.sh:/docker-entrypoint-initdb.d/rabbitmq-init.sh
        healthcheck:
            test: ["CMD", "rabbitmq-diagnostics", "ping"]
            interval: 30s
            timeout: 10s
            retries: 5
