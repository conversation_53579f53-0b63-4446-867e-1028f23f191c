# VolleyStation Integration Implementation Plan

## Executive Summary

This document outlines the implementation plan for integrating VolleyStation with the SportWrench platform. The integration will provide API endpoints for VolleyStation to retrieve event data (schedule, athletes, staff) and submit results data, following the established **SailsJS Actions** pattern used in the v2 API controllers.

## Codebase Analysis Results

### Controller Architecture Patterns

**Legacy Pattern (API directory):**
- Location: `api/controllers/API/`
- Example: `TPCController.js` — traditional SailsJS controller functions
- Used for older integrations like TPC, NCSA, SWB

**Modern Pattern (v2 directory):**
- Location: `api/controllers/v2/API/`
- Example: `baller-tv/events.js` — uses SailsJS Actions
- **Required for all new implementations**

### Existing Third-Party Integration Patterns (examples)

1. **BallerTV Integration** (`api/controllers/v2/API/baller-tv/`)
    - Uses SailsJS Actions pattern
    - Simple API key authentication
    - Event filtering with dedicated database column

2. **ACS Integration** (`api/controllers/v2/API/acs/`)
    - Complex authentication with custom policies
    - Multiple endpoints for different data types

3. **TPC Integration** (`api/controllers/API/TPCController.js`)
    - Legacy pattern but provides reference for data structures
    - Uses `UAExportService.getSchedule()` for schedule data retrieval

## Architecture Plan

### Directory Structure
```
api/controllers/v2/API/volley-station/
├── schedule.js     # GET - Event schedule data (paginated)
├── team-roster.js  # GET - Event team roster data (paginated)
└── results.js      # POST - Submit results data

api/policies/volley-station/
├── auth.js         # Authentication policy
└── rate-limit.js   # Rate limiting policy

userconfig/routes/api/
└── volley-station.js  # Route definitions

config/
└── volleyStation.js   # Configuration file
```

### Endpoint Design

**Base URL Pattern:** `/api/volley-station/v1/`

1. **GET `/api/volley-station/v1/events/:eventId/schedule`**
    - Returns schedule data (matches, teams, courts, timing)
    - Supports pagination with `page` and `limit` query parameters
    - Leverages existing `UAExportService.getSchedule()`

2. **GET `/api/volley-station/v1/events/:eventId/team-roster`**
    - Returns unified team roster data including athletes, staff, and team information
    - Supports pagination with `page` and `limit` query parameters
    - Filtered by event and team status

3. **POST `/api/volley-station/v1/events/:eventId/results`**
    - Accepts match results and scores
    - Validates and persists results data

### Authorization Implementation

**Event-Level Authorization:**
- Condition: `volley_station_enabled: true` in dedicated `event` table column
- Database schema change required:
  ```sql
  ALTER TABLE event ADD COLUMN volley_station_enabled BOOLEAN DEFAULT FALSE;
  ```
- Query pattern:
  ```sql
  WHERE e.volley_station_enabled = TRUE
  ```
- Return **403 Forbidden** if not enabled

## Rate Limiting

Implement Redis-based rate limiting following existing codebase patterns:
- **Configuration:** 100 requests per minute per access token
- **Implementation:** Custom policy using Redis counters
- **Headers:** Include rate limit status in response headers
- **Error Response:** 429 status with retry information

## Pagination Support

Both `/schedule` and `/team-roster` endpoints support pagination:
- **Query Parameters:** `page` (default: 1), `limit` (default: 50, max: 100)
- **Response Format:** Includes data array and pagination metadata
- **Implementation:** Following existing ACS teams list patterns

**API Key Authentication:**
- Header: `Authorization: vs_api_key_value`
- Validated against `sails.config.volleyStation.apiKey`
- Applied as a policy across all VolleyStation endpoints

## Implementation Steps

### Phase 1: Foundation Setup
1. Add `volley_station_enabled` column to event table
2. Create configuration file `config/volleyStation.js` with rate limiting config
3. Implement authentication policy `api/policies/volley-station/auth.js`
4. Implement rate limiting policy `api/policies/volley-station/rate-limit.js`
5. Define routes in `userconfig/routes/api/volley-station.js`
6. Create base controller directory structure

### Phase 2: Data Retrieval Endpoints
1. Implement **schedule** endpoint with pagination (leveraging `UAExportService`)
2. Implement **team-roster** endpoint with pagination (unified athletes and staff data)
3. Add strict input validation and error handling

### Phase 3: Results Submission
1. Design and finalize results data schema
2. Implement results submission endpoint
3. Add validation and storage logic
4. Ensure consistent error and success responses

### Phase 4: Testing & Documentation
1. Develop comprehensive API tests (unit + integration)
2. Create OpenAPI/Swagger documentation for endpoints
3. Test different authorization and failure scenarios

## Security Considerations
1. **API Key Management:** Store in environment variables, never in code
2. **Event Authorization:** Always verify event access using dedicated column
3. **Monitoring:** Log and audit API access for security and debugging
