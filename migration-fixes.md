# Knex Migration Fixes for Local Development

## Summary

This document contains SQL scripts and fixes to resolve migration issues encountered when synchronizing a local development database with the latest migration state. The issues stem from missing migration files and database schema inconsistencies.

## Issues Encountered

### 1. Missing Migration Files
The following migration files were missing locally and have been created as stub files:
- `20230209130636_SW-2767.js`
- `20230323150542_SW-2943.js`
- `20230329113145_SW-2943.js`
- `20230330153504_SW-2943.js`
- `20230403150310_SW-2943_ticket_wallet.js`
- `20230409181610_SW-2968.js`
- `20240730052128_SW-3260.js`

### 2. Table Creation Conflicts
Several migrations failed because tables already exist in the database:

#### Fixed Migrations:
- `20240904054744_SW-3671.js` - `email_editor_image` table
- `20240919035848_SW-3721.js` - `payment_session` and `payment_session_team` tables
- `20241008104829_SW-3757.js` - `sales_hub_webhook_event` table
- `20250205034102_SW-4009.js` - `sales_hub_refund` table

#### Remaining Issues:
- `20250218153453_SW-4096.js` - `event_exhibitor_invoice` table

### 3. Constraint Issues
- `20240924050822_SW-3724.js` - Foreign key constraint modification failed due to missing primary key

### 4. Column Modification Issues
- `20241219162544_SW-3926.js` - Column type change blocked by materialized view dependency
- `20250217215945_SW-4077.js` - Column rename failed because column doesn't exist

## SQL Scripts for Manual Fixes

### Fix Missing Primary Key for payment_session Table

```sql
-- Check if primary key exists
SELECT constraint_name 
FROM information_schema.table_constraints 
WHERE table_name = 'payment_session' 
AND constraint_type = 'PRIMARY KEY';

-- If no primary key exists, add it
ALTER TABLE payment_session 
ADD CONSTRAINT payment_session_pkey 
PRIMARY KEY (payment_session_id);
```

### Fix Foreign Key Constraint for payment_session_team

```sql
-- Drop existing constraint if it exists
ALTER TABLE payment_session_team 
DROP CONSTRAINT IF EXISTS payment_session_team_payment_session_id_fkey;

-- Add the constraint with CASCADE delete
ALTER TABLE payment_session_team 
ADD CONSTRAINT payment_session_team_payment_session_id_fkey 
FOREIGN KEY (payment_session_id) 
REFERENCES payment_session(payment_session_id) 
ON DELETE CASCADE;
```

### Fix Materialized View Dependency for roster_club.code

```sql
-- Drop the materialized view temporarily
DROP MATERIALIZED VIEW IF EXISTS club_search_index;

-- Change the column type
ALTER TABLE roster_club ALTER COLUMN code TYPE TEXT;

-- Recreate the materialized view (you'll need the original view definition)
-- This requires the original CREATE MATERIALIZED VIEW statement
```

### Create Missing Tables

```sql
-- Create event_exhibitor_invoice table if it doesn't exist
CREATE TABLE IF NOT EXISTS "public"."event_exhibitor_invoice" (
    "event_exhibitor_invoice_id"    INT GENERATED ALWAYS AS IDENTITY,
    "created"                       TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "modified"                      TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "event_exhibitor_id"            INT NOT NULL,
    "purchase_id"                   INT NOT NULL,
    "event_dates"                   JSONB DEFAULT '{}'::JSONB,
    "booths"                        INTEGER[] DEFAULT '{}'::INTEGER[],
    "comment"                       TEXT,
    PRIMARY KEY("event_exhibitor_invoice_id")
);

-- Create trigger if it doesn't exist
DROP TRIGGER IF EXISTS update_event_exhibitor_invoice_modified ON "public"."event_exhibitor_invoice";
CREATE TRIGGER update_event_exhibitor_invoice_modified 
BEFORE UPDATE ON "public"."event_exhibitor_invoice" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();
```

### Add Missing Columns

```sql
-- Add is_impact column if it doesn't exist (for sportengine_adult_role)
ALTER TABLE sportengine_adult_role 
ADD COLUMN IF NOT EXISTS is_impact BOOLEAN DEFAULT FALSE;

-- Remove is_chaperone column if it exists
ALTER TABLE sportengine_adult_role 
DROP COLUMN IF EXISTS is_chaperone;
```

## Next Steps

1. **Run the SQL scripts above** in your PostgreSQL database to fix the structural issues
2. **Re-run the migration**: `npm run migrate-main`
3. **Monitor for additional failures** and add fixes to this document as needed

## Environment Setup

Make sure you have the correct environment variable set:
```bash
export SW_DB=postgresql://postgres:<EMAIL>:5433/sw_dev
```

## Status

- ✅ Created stub migration files
- ✅ Fixed table creation conflicts for 4 migrations
- ⚠️ Need to run SQL scripts for remaining structural issues
- ⚠️ Need to handle materialized view dependency manually
- ⚠️ May encounter additional issues in later migrations

## Notes

This is a temporary solution for local development. The actual migration files should be obtained from the proper source (staging/production) for a permanent fix.
